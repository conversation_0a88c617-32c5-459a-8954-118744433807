; See 'haraka -h tls'

; key=tls_key.pem
; cert=tls_cert.pem
; dhparam=dhparams.pem

; ciphers: a list of permitted ciphers
; The default cipher list is provided by node.js and is considered secure at
; the time of that versions release. If you have problems with the default cipher
; list, try enabling this "kinda high but more compatible" setting.
; ciphers=ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:DHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA:ECDHE-RSA-AES128-SHA:DHE-RSA-AES256-SHA256:DHE-RSA-AES128-SHA256:DHE-RSA-AES256-SHA:DHE-RSA-AES128-SHA:ECDHE-RSA-DES-CBC3-SHA:EDH-RSA-DES-CBC3-SHA:AES256-GCM-SHA384:AES128-GCM-SHA256:AES256-SHA256:AES128-SHA256:AES256-SHA:AES128-SHA:DES-CBC3-SHA:HIGH:!aNULL:!eNULL:!EXPORT:!DES:!MD5:!PSK:!RC4

; minimum TLS version (node.js 11.4+ required)
; Allowed values are 'TLSv1.3', 'TLSv1.2', 'TLSv1.1', or 'TLSv1'
; The default value is node.js's tls.DEFAULT_MIN_VERSION
; minVersion=TLSv1

; honorCipherOrder=true
; rejectUnauthorized=false
; requestCert=true
; requestOCSP=false

; rejectUnauthorized above requires verified TLS certs on EVERY TLS connection. When
; rejectUnauthorized=false (default), you can require verified TLS certs on only the
; ports you specify.
; requireAuthorized[]=465
; requireAuthorized[]=587

; send client certificate(s). If you use this setting and value it, report
; your use case at https://github.com/haraka/Haraka/issues/2693
; mutual_tls=false

; haraka will not advertise STARTTLS on these ports it is listening on
; no_starttls_ports[]=2525

[redis]
; options in this block require redis to be enabled in config/plugins.

; Remember when a remote fails STARTTLS, the next time they/we connect,
;     don't offer/use STARTTLS option (so message gets delivered).
;     pro: increases mail reliability
;     con: reduces security
; outbound only warning: **you must restart haraka** after changing this option
; default: false
; disable_for_failed_hosts=true

; The following section applies to outbound only:
; host = 127.0.0.1
; "TLS NO-GO" db
; db = 3
; TLS NO-GO Expiry time in seconds
; disable_expiry = 604800

; TLS NO-GO Inbound expiry time in seconds
; disable_inbound_expiry = 3600


; no_tls_hosts - disable TLS for servers with broken TLS. (applies to inbound only)
[no_tls_hosts]
; 127.0.0.1
; ***********
; **********/16


; hosts that require us to present a cert signed by a CA we both trust
[mutual_auth_hosts]
;travel.state.gov                     ; use default TLS cert
;xo.huggable.gov=special.my-tld.com   ; specify cert by CN


; these hosts request mutual TLS and reject our TLS certificate
[mutual_auth_hosts_exclude]
;bofh.no-such-agency.gov


[outbound]
; key=tls_key.pem
; cert=tls_cert.pem
; dhparam=dhparams.pem
; no_tls_hosts[]=127.0.0.1
; no_tls_hosts[]=***********

; and other options from [main] section above
