; address to listen on (default: all IPv6 and IPv4 addresses, port 25)
; use "[::0]:25" to listen on IPv6 and IPv4 (not all OSes)
listen=[::0]:587

; Note you can listen on multiple IPs/ports using commas:
;listen=127.0.0.1:2529,*********:2529,*********:2530

; public IP address (default: none)
; If your machine is behind a NAT, some plugins (SPF, GeoIP) gain features
; if they know the servers public IP. If 'stun' is installed, <PERSON><PERSON> will
; try to figure it out. If that doesn't work, set it here.
;public_ip=N.N.N.N

; Time in seconds to let sockets be idle with no activity
;inactivity_timeout=300

; Drop privileges to this user/group
;user=smtp
;group=smtp

; Don't stop <PERSON><PERSON> if plugins fail to compile
;ignore_bad_plugins=0

; Run using cluster to fork multiple backend processes
; Ref: https://github.com/haraka/Haraka/wiki/Performance-Tuning
nodes=2

; Daemonize
;daemonize=true
;daemon_log_file=/var/log/haraka.log
;daemon_pid_file=/var/run/haraka.pid

; Spooling
; Save memory by spooling large messages to disk
;spool_dir=/var/spool/haraka
; Specify -1 to never spool to disk
; Specify 0 to always spool to disk
; Otherwise specify a size in bytes, once reached the
; message will be spooled to disk to save memory.
;spool_after=

; Force Shutdown Timeout
; - Haraka tries to close down gracefully, but if everything is shut down
;   after this time it will hard close. 30s is usually long enough to
;   wait for outbound connections to finish.
;force_shutdown_timeout=30

; SMTP service extensions: https://tools.ietf.org/html/rfc1869
; strict_rfc1869 = false

; Advertise support for SMTPUTF8 (RFC-6531)
;smtputf8=true

[headers]
;add_received=true
;clean_auth_results=true

;show_version=true

; replace max_header_lines
max_lines=1000

; replace max_received_count
max_received=100
