; watch - a web interface for viewing Haraka activity

; Sampling:Limit display to 1 connection per second (Default: false)
; sampling=false

[wss]
; url (Default: same URL as HTTP client used)
; The WebSocket client will attempt to connect via the same URI (changing only
; the scheme) as the initial HTTP connection. WSS is stricter than typical
; HTTP so the scheme and hostname *must* match else it silently fails.
;
; url=wss://mail.example.com/
