
; listen: the HTTP address:port(s) to listen on
; default: [::]:80 (port 80 on all IPv4 and IPv6 addresses)
; listen=[::]:80

; listen can also be a unix socket path, with an optional 3-digit permission mask
; e.g. listen=/path/to/some.sock or listen=/path/to/some.sock:777
; if no mask is specified, the default permissions are determined by the umask.

; docroot: the directory where web content is served from
;docroot=/usr/local/haraka/html
